# Simple Python GUI

A basic Python GUI application built with tkinter that demonstrates common GUI elements and functionality.

## Features

- **Text Input**: Enter your name and get a personalized greeting
- **Counter**: Increment, decrement, and reset a counter
- **Text Area**: Multi-line text input with scrollbar
- **File Operations**: Save and load text files
- **Message Boxes**: Information, warning, and error dialogs

## Requirements

- Python 3.x (tkinter is included with most Python installations)
- No additional packages required

## How to Run

1. Make sure you have Python installed on your system
2. Navigate to the project directory
3. Run the application:

```bash
python simple_gui.py
```

or

```bash
python3 simple_gui.py
```

## GUI Components Demonstrated

- **Labels**: Display text and information
- **Entry widgets**: Single-line text input
- **Buttons**: Trigger actions and events
- **Text widget**: Multi-line text input with scrollbar
- **Frames**: Organize and group widgets
- **Message boxes**: Show dialogs for user interaction
- **File dialogs**: Open and save file operations

## Usage

1. **Greeting**: Enter your name in the text field and click "Say Hello"
2. **Counter**: Use the increment/decrement buttons to change the counter value
3. **Notes**: Type in the text area and use the buttons to:
   - Clear all text
   - Save text to a file
   - Load text from a file
4. **About**: Click the About button for more information

## Code Structure

The application is organized as a class-based GUI with:
- `SimpleGUI` class containing all the GUI logic
- Separate methods for each functionality
- Proper event handling and error management
- Clean separation of UI creation and business logic

This is a great starting point for learning Python GUI development with tkinter!
