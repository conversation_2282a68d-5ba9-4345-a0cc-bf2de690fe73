#!/usr/bin/env python3
"""
Simple Python GUI Application using tkinter
A basic example with common GUI elements
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os

class SimpleGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Simple Python GUI")
        self.root.geometry("500x400")
        self.root.resizable(True, True)
        
        # Variables
        self.name_var = tk.StringVar()
        self.counter = 0
        
        self.create_widgets()
        
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title label
        title_label = ttk.Label(main_frame, text="Simple Python GUI Demo", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Name input section
        ttk.Label(main_frame, text="Your Name:").grid(row=1, column=0, sticky=tk.W, pady=5)
        name_entry = ttk.Entry(main_frame, textvariable=self.name_var, width=30)
        name_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # Greeting button
        greet_btn = ttk.Button(main_frame, text="Say Hello", command=self.greet_user)
        greet_btn.grid(row=2, column=0, columnspan=2, pady=10)
        
        # Counter section
        ttk.Label(main_frame, text="Counter:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.counter_label = ttk.Label(main_frame, text="0", font=("Arial", 12, "bold"))
        self.counter_label.grid(row=3, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        
        # Counter buttons frame
        counter_frame = ttk.Frame(main_frame)
        counter_frame.grid(row=4, column=0, columnspan=2, pady=10)
        
        ttk.Button(counter_frame, text="Increment", 
                  command=self.increment_counter).pack(side=tk.LEFT, padx=5)
        ttk.Button(counter_frame, text="Decrement", 
                  command=self.decrement_counter).pack(side=tk.LEFT, padx=5)
        ttk.Button(counter_frame, text="Reset", 
                  command=self.reset_counter).pack(side=tk.LEFT, padx=5)
        
        # Text area section
        ttk.Label(main_frame, text="Notes:").grid(row=5, column=0, sticky=(tk.W, tk.N), pady=(20, 5))
        
        # Text widget with scrollbar
        text_frame = ttk.Frame(main_frame)
        text_frame.grid(row=5, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(20, 5), padx=(10, 0))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        self.text_area = tk.Text(text_frame, height=8, width=40, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.text_area.yview)
        self.text_area.configure(yscrollcommand=scrollbar.set)
        
        self.text_area.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=6, column=0, columnspan=2, pady=20)
        
        ttk.Button(buttons_frame, text="Clear Text", 
                  command=self.clear_text).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Save Text", 
                  command=self.save_text).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Load Text", 
                  command=self.load_text).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="About", 
                  command=self.show_about).pack(side=tk.LEFT, padx=5)
        
        # Configure main_frame grid weights
        main_frame.rowconfigure(5, weight=1)
        
    def greet_user(self):
        name = self.name_var.get().strip()
        if name:
            messagebox.showinfo("Greeting", f"Hello, {name}! Welcome to the Simple GUI!")
        else:
            messagebox.showwarning("Warning", "Please enter your name first!")
    
    def increment_counter(self):
        self.counter += 1
        self.counter_label.config(text=str(self.counter))
    
    def decrement_counter(self):
        self.counter -= 1
        self.counter_label.config(text=str(self.counter))
    
    def reset_counter(self):
        self.counter = 0
        self.counter_label.config(text=str(self.counter))
    
    def clear_text(self):
        self.text_area.delete(1.0, tk.END)
    
    def save_text(self):
        content = self.text_area.get(1.0, tk.END)
        if content.strip():
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            if filename:
                try:
                    with open(filename, 'w') as file:
                        file.write(content)
                    messagebox.showinfo("Success", f"Text saved to {os.path.basename(filename)}")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to save file: {str(e)}")
        else:
            messagebox.showwarning("Warning", "No text to save!")
    
    def load_text(self):
        filename = filedialog.askopenfilename(
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r') as file:
                    content = file.read()
                self.text_area.delete(1.0, tk.END)
                self.text_area.insert(1.0, content)
                messagebox.showinfo("Success", f"Text loaded from {os.path.basename(filename)}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load file: {str(e)}")
    
    def show_about(self):
        messagebox.showinfo("About", 
                           "Simple Python GUI\n\n"
                           "A basic tkinter application demonstrating:\n"
                           "• Text input and labels\n"
                           "• Buttons and event handling\n"
                           "• Counter functionality\n"
                           "• Text area with scrollbar\n"
                           "• File operations\n"
                           "• Message boxes\n\n"
                           "Built with Python tkinter")

def main():
    root = tk.Tk()
    app = SimpleGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
